# 原神进程文件监控器

这是一个使用Python和tkinter开发的GUI应用程序，用于监控原神游戏进程访问的所有文件。

## 功能特性

- GUI界面操作简单
- 实时监控原神进程的文件访问
- 显示详细的文件访问日志
- 统计监控时间和文件访问次数
- 支持保存日志到文件
- 管理员权限运行以获得完整监控功能

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1：以管理员权限运行（推荐）
双击运行 `run_as_admin.bat` 文件

### 方法2：直接运行Python文件
```bash
python yuanshen_monitor.py
```

## 使用步骤

1. 先启动原神游戏
2. 运行监控程序
3. 点击"开始监控"按钮
4. 程序会自动检测原神进程并开始监控
5. 在日志区域查看文件访问记录
6. 可以随时点击"停止监控"停止监控
7. 使用"保存日志"按钮将监控结果保存到文件

## 注意事项

- 建议以管理员权限运行以获得完整的监控功能
- 需要先启动原神游戏才能开始监控
- 程序会自动检测原神进程（支持YuanShen.exe、GenshinImpact.exe等）
- 监控过程中会调用系统的resmon（资源监视器）
- 日志文件会保存在程序同目录下

## 系统要求

- Windows操作系统
- Python 3.6+
- tkinter（通常随Python安装）
- psutil库
