import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import subprocess
import threading
import time
import os
import sys
import ctypes
from datetime import datetime
import psutil
import re

class YuanshenMonitor:
    def __init__(self, root):
        self.root = root
        self.root.title("原神进程文件监控器")
        self.root.geometry("1200x800")

        self.monitoring = False
        self.yuanshen_pid = None
        self.log_data = []
        self.file_access_count = {}
        self.prefix_filter = ""
        self.suffix_filter = ""
        self.blacklist = set()
        self.filtered_files_count = 0
        self.last_scan_files = set()
        self.scan_interval = 2

        self.setup_ui()
        self.check_admin_privileges()
        
    def check_admin_privileges(self):
        try:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if not is_admin:
                messagebox.showwarning("权限警告", "建议以管理员身份运行此程序以获得完整的监控功能")
        except:
            pass
    
    def setup_ui(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

        ttk.Label(main_frame, text="原神进程监控", font=("Arial", 16, "bold")).grid(row=0, column=0, columnspan=3, pady=(0, 20))

        filter_frame = ttk.LabelFrame(main_frame, text="文件过滤设置", padding="5")
        filter_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(filter_frame, text="前缀过滤:").grid(row=0, column=0, padx=(0, 5), sticky=tk.W)
        self.prefix_entry = ttk.Entry(filter_frame, width=20)
        self.prefix_entry.grid(row=0, column=1, padx=(0, 20), sticky=tk.W)
        self.prefix_entry.insert(0, "*")

        ttk.Label(filter_frame, text="后缀过滤:").grid(row=0, column=2, padx=(0, 5), sticky=tk.W)
        self.suffix_entry = ttk.Entry(filter_frame, width=20)
        self.suffix_entry.grid(row=0, column=3, padx=(0, 20), sticky=tk.W)
        self.suffix_entry.insert(0, "*")

        ttk.Button(filter_frame, text="应用过滤", command=self.apply_filter).grid(row=0, column=4, padx=(10, 0))

        ttk.Button(filter_frame, text="管理黑名单", command=self.show_blacklist_manager).grid(row=0, column=5, padx=(10, 0))

        ttk.Label(filter_frame, text="扫描间隔(秒):").grid(row=0, column=6, padx=(20, 5), sticky=tk.W)
        self.interval_var = tk.StringVar(value="2")
        interval_combo = ttk.Combobox(filter_frame, textvariable=self.interval_var, values=["1", "2", "3", "5"], width=5, state="readonly")
        interval_combo.grid(row=0, column=7, padx=(0, 10))
        interval_combo.bind("<<ComboboxSelected>>", self.update_scan_interval)

        ttk.Label(filter_frame, text="提示: 使用*或留空表示任意，例如: 前缀'C:\\Game' 后缀'.dll' | 右键日志可添加/批量添加黑名单",
                 font=("Arial", 8)).grid(row=1, column=0, columnspan=8, pady=(5, 0), sticky=tk.W)

        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 10))

        self.start_btn = ttk.Button(control_frame, text="开始监控", command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_btn = ttk.Button(control_frame, text="清空日志", command=self.clear_log)
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.save_btn = ttk.Button(control_frame, text="保存日志", command=self.save_log)
        self.save_btn.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(control_frame, text="进程状态:").pack(side=tk.LEFT, padx=(20, 5))
        self.status_label = ttk.Label(control_frame, text="未检测到原神进程", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        log_frame = ttk.LabelFrame(main_frame, text="文件访问日志 (显示调用次数)", padding="5")
        log_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=25, font=("Consolas", 9))
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.setup_context_menu()

        stats_frame = ttk.LabelFrame(main_frame, text="统计信息", padding="5")
        stats_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        self.stats_label = ttk.Label(stats_frame, text="文件访问次数: 0 | 监控时间: 0秒 | 当前过滤: 前缀(*) 后缀(*) | 黑名单: 0")
        self.stats_label.pack()

    def setup_context_menu(self):
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="添加到黑名单", command=self.add_to_blacklist)
        self.context_menu.add_command(label="批量添加到黑名单", command=self.batch_add_to_blacklist)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="复制选中文本", command=self.copy_selected)

        self.log_text.bind("<Button-3>", self.show_context_menu)
        
    def apply_filter(self):
        prefix = self.prefix_entry.get().strip()
        suffix = self.suffix_entry.get().strip()

        self.prefix_filter = "" if prefix == "*" or prefix == "" else prefix
        self.suffix_filter = "" if suffix == "*" or suffix == "" else suffix

        prefix_display = self.prefix_filter if self.prefix_filter else "*"
        suffix_display = self.suffix_filter if self.suffix_filter else "*"

        self.update_stats_display()
        messagebox.showinfo("过滤设置", f"过滤器已应用:\n前缀: {prefix_display}\n后缀: {suffix_display}")

    def update_scan_interval(self, event=None):
        try:
            self.scan_interval = int(self.interval_var.get())
            if self.monitoring:
                messagebox.showinfo("设置更新", f"扫描间隔已更新为 {self.scan_interval} 秒\n将在下次扫描时生效")
        except ValueError:
            self.scan_interval = 2

    def show_context_menu(self, event):
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def add_to_blacklist(self):
        try:
            selected_text = self.log_text.get(tk.SEL_FIRST, tk.SEL_LAST)
            file_path = self.extract_file_path_from_log(selected_text)
            if file_path:
                self.blacklist.add(file_path)
                self.update_stats_display()
                messagebox.showinfo("黑名单", f"已添加到黑名单:\n{file_path}")
            else:
                messagebox.showwarning("警告", "未能从选中文本中提取文件路径")
        except tk.TclError:
            messagebox.showwarning("警告", "请先选中包含文件路径的文本")

    def batch_add_to_blacklist(self):
        try:
            selected_text = self.log_text.get(tk.SEL_FIRST, tk.SEL_LAST)
            file_paths = self.extract_all_file_paths_from_log(selected_text)
            if file_paths:
                added_count = 0
                for file_path in file_paths:
                    if file_path not in self.blacklist:
                        self.blacklist.add(file_path)
                        added_count += 1

                self.update_stats_display()
                messagebox.showinfo("批量黑名单", f"已批量添加 {added_count} 个文件到黑名单\n总共处理 {len(file_paths)} 个文件路径")
            else:
                messagebox.showwarning("警告", "未能从选中文本中提取文件路径")
        except tk.TclError:
            messagebox.showwarning("警告", "请先选中包含文件路径的文本")

    def extract_file_path_from_log(self, text):
        import re
        match = re.search(r'文件访问: (.+?) \(调用次数:', text)
        if match:
            return match.group(1)
        match = re.search(r'文件访问: (.+)', text)
        if match:
            return match.group(1).strip()
        return None

    def extract_all_file_paths_from_log(self, text):
        import re
        file_paths = []

        matches = re.findall(r'文件访问: (.+?) \(调用次数:', text)
        file_paths.extend(matches)

        matches = re.findall(r'文件访问: ([^\n\r]+)', text)
        for match in matches:
            clean_path = match.strip()
            if clean_path not in file_paths and not clean_path.endswith(')'):
                file_paths.append(clean_path)

        return list(set(file_paths))

    def copy_selected(self):
        try:
            selected_text = self.log_text.get(tk.SEL_FIRST, tk.SEL_LAST)
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
            messagebox.showinfo("复制", "已复制选中文本到剪贴板")
        except tk.TclError:
            messagebox.showwarning("警告", "请先选中要复制的文本")

    def matches_filter(self, file_path):
        if file_path in self.blacklist:
            self.filtered_files_count += 1
            return False
        if self.prefix_filter and not file_path.startswith(self.prefix_filter):
            return False
        if self.suffix_filter and not file_path.endswith(self.suffix_filter):
            return False
        return True

    def find_yuanshen_process(self):
        yuanshen_names = ["YuanShen.exe", "GenshinImpact.exe", "原神.exe"]
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'] in yuanshen_names:
                    return proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return None
    
    def start_monitoring(self):
        self.yuanshen_pid = self.find_yuanshen_process()
        if not self.yuanshen_pid:
            messagebox.showerror("错误", "未找到原神进程，请先启动原神游戏")
            return

        self.apply_filter()

        self.monitoring = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.status_label.config(text=f"正在监控原神进程 (PID: {self.yuanshen_pid})", foreground="green")

        self.file_access_count.clear()
        self.filtered_files_count = 0

        self.monitor_thread = threading.Thread(target=self.monitor_process, daemon=True)
        self.monitor_thread.start()

        self.start_time = time.time()
        self.update_stats()
    
    def stop_monitoring(self):
        self.monitoring = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.status_label.config(text="监控已停止", foreground="orange")
    
    def monitor_process(self):
        try:
            cmd = f'resmon.exe /pid {self.yuanshen_pid}'
            
            self.log_message(f"开始监控原神进程 (PID: {self.yuanshen_pid})")
            self.log_message(f"启动资源监视器命令: {cmd}")
            
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, text=True)
            
            while self.monitoring:
                if not self.is_process_running(self.yuanshen_pid):
                    self.log_message("原神进程已结束")
                    break
                
                self.monitor_file_access()
                time.sleep(self.scan_interval)
                
        except Exception as e:
            self.log_message(f"监控过程中发生错误: {str(e)}")
    
    def is_process_running(self, pid):
        try:
            return psutil.pid_exists(pid)
        except:
            return False
    
    def monitor_file_access(self):
        try:
            proc = psutil.Process(self.yuanshen_pid)

            try:
                open_files = proc.open_files()
                current_files = set()

                for file_info in open_files:
                    file_path = file_info.path
                    current_files.add(file_path)

                    if self.matches_filter(file_path):
                        if file_path in self.file_access_count:
                            if file_path not in self.last_scan_files:
                                self.file_access_count[file_path] += 1
                                self.update_file_count_display_optimized(file_path)
                        else:
                            self.file_access_count[file_path] = 1
                            timestamp = datetime.now().strftime("%H:%M:%S")
                            log_entry = f"[{timestamp}] 文件访问: {file_path} (调用次数: 1)"
                            self.log_message(log_entry)

                self.last_scan_files = current_files

            except (psutil.AccessDenied, psutil.NoSuchProcess):
                pass

        except Exception as e:
            if self.monitoring:
                self.log_message(f"获取文件信息时出错: {str(e)}")
    
    def update_file_count_display_optimized(self, file_path):
        count = self.file_access_count[file_path]

        if not hasattr(self, '_update_queue'):
            self._update_queue = {}

        self._update_queue[file_path] = count

        if not hasattr(self, '_update_scheduled') or not self._update_scheduled:
            self._update_scheduled = True
            self.root.after(500, self._batch_update_display)

    def _batch_update_display(self):
        if hasattr(self, '_update_queue') and self._update_queue:
            content = self.log_text.get(1.0, tk.END)
            lines = content.split('\n')

            updates_made = False
            for file_path, count in self._update_queue.items():
                for i, line in enumerate(lines):
                    if file_path in line and "调用次数:" in line:
                        old_count_match = re.search(r'调用次数: (\d+)', line)
                        if old_count_match:
                            new_line = re.sub(r'调用次数: \d+', f'调用次数: {count}', line)
                            lines[i] = new_line
                            updates_made = True
                            break

            if updates_made:
                self.log_text.delete(1.0, tk.END)
                self.log_text.insert(1.0, '\n'.join(lines))
                self.log_text.see(tk.END)

            self._update_queue.clear()

        self._update_scheduled = False

    def log_message(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = {
            'timestamp': timestamp,
            'message': message
        }
        self.log_data.append(log_entry)

        self.root.after(0, lambda: self.update_log_display(message))

    def update_log_display(self, message):
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
    
    def update_stats(self):
        if hasattr(self, 'start_time'):
            elapsed_time = int(time.time() - self.start_time)
            unique_files = len(self.file_access_count)
            total_accesses = sum(self.file_access_count.values())

            prefix_display = self.prefix_filter if self.prefix_filter else "*"
            suffix_display = self.suffix_filter if self.suffix_filter else "*"

            self.stats_label.config(text=f"独立文件: {unique_files} | 总访问次数: {total_accesses} | 监控时间: {elapsed_time}秒 | 过滤: 前缀({prefix_display}) 后缀({suffix_display}) | 黑名单: {len(self.blacklist)} | 被过滤: {self.filtered_files_count}")

        if self.monitoring:
            self.root.after(1000, self.update_stats)

    def update_stats_display(self):
        if hasattr(self, 'start_time'):
            elapsed_time = int(time.time() - self.start_time)
            unique_files = len(self.file_access_count)
            total_accesses = sum(self.file_access_count.values())
        else:
            elapsed_time = 0
            unique_files = 0
            total_accesses = 0

        prefix_display = self.prefix_filter if self.prefix_filter else "*"
        suffix_display = self.suffix_filter if self.suffix_filter else "*"

        self.stats_label.config(text=f"独立文件: {unique_files} | 总访问次数: {total_accesses} | 监控时间: {elapsed_time}秒 | 过滤: 前缀({prefix_display}) 后缀({suffix_display}) | 黑名单: {len(self.blacklist)} | 被过滤: {self.filtered_files_count}")
    
    def show_blacklist_manager(self):
        blacklist_window = tk.Toplevel(self.root)
        blacklist_window.title("黑名单管理")
        blacklist_window.geometry("800x500")
        blacklist_window.transient(self.root)
        blacklist_window.grab_set()

        main_frame = ttk.Frame(blacklist_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text="黑名单文件列表", font=("Arial", 12, "bold")).pack(pady=(0, 10))

        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.blacklist_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set, font=("Consolas", 9))
        self.blacklist_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.blacklist_listbox.yview)

        for item in sorted(self.blacklist):
            self.blacklist_listbox.insert(tk.END, item)

        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="删除选中", command=self.remove_from_blacklist).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清空黑名单", command=self.clear_blacklist).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="导出黑名单", command=self.export_blacklist).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="导入黑名单", command=self.import_blacklist).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=blacklist_window.destroy).pack(side=tk.RIGHT)

        self.blacklist_window = blacklist_window

    def remove_from_blacklist(self):
        selection = self.blacklist_listbox.curselection()
        if selection:
            item = self.blacklist_listbox.get(selection[0])
            self.blacklist.discard(item)
            self.blacklist_listbox.delete(selection[0])
            self.update_stats_display()
            messagebox.showinfo("删除", f"已从黑名单中删除:\n{item}")
        else:
            messagebox.showwarning("警告", "请先选择要删除的项目")

    def clear_blacklist(self):
        if messagebox.askyesno("确认", "确定要清空所有黑名单项目吗？"):
            self.blacklist.clear()
            self.blacklist_listbox.delete(0, tk.END)
            self.update_stats_display()
            messagebox.showinfo("清空", "黑名单已清空")

    def export_blacklist(self):
        if not self.blacklist:
            messagebox.showinfo("提示", "黑名单为空，无法导出")
            return

        filename = f"blacklist_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("原神监控器黑名单\n")
                f.write("=" * 30 + "\n")
                for item in sorted(self.blacklist):
                    f.write(f"{item}\n")
            messagebox.showinfo("导出成功", f"黑名单已导出到: {filename}")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出黑名单失败: {str(e)}")

    def import_blacklist(self):
        from tkinter import filedialog
        filename = filedialog.askopenfilename(
            title="选择黑名单文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                imported_count = 0
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith("=") and "黑名单" not in line:
                        if line not in self.blacklist:
                            self.blacklist.add(line)
                            self.blacklist_listbox.insert(tk.END, line)
                            imported_count += 1

                self.update_stats_display()
                messagebox.showinfo("导入成功", f"成功导入 {imported_count} 个黑名单项目")
            except Exception as e:
                messagebox.showerror("导入失败", f"导入黑名单失败: {str(e)}")

    def clear_log(self):
        self.log_text.delete(1.0, tk.END)
        self.log_data.clear()
        self.file_access_count.clear()
        self.filtered_files_count = 0
        self.update_stats_display()

    def save_log(self):
        if not self.log_data and not self.file_access_count:
            messagebox.showinfo("提示", "没有日志数据可保存")
            return

        filename = f"yuanshen_monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("原神进程文件访问监控日志\n")
                f.write("=" * 50 + "\n")
                f.write(f"过滤设置: 前缀({self.prefix_filter if self.prefix_filter else '*'}) 后缀({self.suffix_filter if self.suffix_filter else '*'})\n")
                f.write(f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n\n")

                f.write("文件访问统计:\n")
                f.write("-" * 30 + "\n")
                for file_path, count in sorted(self.file_access_count.items(), key=lambda x: x[1], reverse=True):
                    f.write(f"调用次数: {count:4d} | {file_path}\n")

                f.write("\n" + "=" * 50 + "\n")
                f.write("详细日志:\n")
                f.write("-" * 30 + "\n")
                for entry in self.log_data:
                    f.write(f"[{entry['timestamp']}] {entry['message']}\n")

            messagebox.showinfo("成功", f"日志已保存到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {str(e)}")

def main():
    root = tk.Tk()
    app = YuanshenMonitor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
