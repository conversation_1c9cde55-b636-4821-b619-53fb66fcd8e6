import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import subprocess
import threading
import time
import os
import sys
import ctypes
from datetime import datetime
import psutil

class YuanshenMonitor:
    def __init__(self, root):
        self.root = root
        self.root.title("原神进程文件监控器")
        self.root.geometry("1000x700")
        
        self.monitoring = False
        self.yuanshen_pid = None
        self.log_data = []
        
        self.setup_ui()
        self.check_admin_privileges()
        
    def check_admin_privileges(self):
        try:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if not is_admin:
                messagebox.showwarning("权限警告", "建议以管理员身份运行此程序以获得完整的监控功能")
        except:
            pass
    
    def setup_ui(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        ttk.Label(main_frame, text="原神进程监控", font=("Arial", 16, "bold")).grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_btn = ttk.Button(control_frame, text="开始监控", command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_btn = ttk.Button(control_frame, text="清空日志", command=self.clear_log)
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.save_btn = ttk.Button(control_frame, text="保存日志", command=self.save_log)
        self.save_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(control_frame, text="进程状态:").pack(side=tk.LEFT, padx=(20, 5))
        self.status_label = ttk.Label(control_frame, text="未检测到原神进程", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        log_frame = ttk.LabelFrame(main_frame, text="文件访问日志", padding="5")
        log_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=25)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        stats_frame = ttk.LabelFrame(main_frame, text="统计信息", padding="5")
        stats_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.stats_label = ttk.Label(stats_frame, text="文件访问次数: 0 | 监控时间: 0秒")
        self.stats_label.pack()
        
    def find_yuanshen_process(self):
        yuanshen_names = ["YuanShen.exe", "GenshinImpact.exe", "原神.exe"]
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'] in yuanshen_names:
                    return proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return None
    
    def start_monitoring(self):
        self.yuanshen_pid = self.find_yuanshen_process()
        if not self.yuanshen_pid:
            messagebox.showerror("错误", "未找到原神进程，请先启动原神游戏")
            return
        
        self.monitoring = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.status_label.config(text=f"正在监控原神进程 (PID: {self.yuanshen_pid})", foreground="green")
        
        self.monitor_thread = threading.Thread(target=self.monitor_process, daemon=True)
        self.monitor_thread.start()
        
        self.start_time = time.time()
        self.update_stats()
    
    def stop_monitoring(self):
        self.monitoring = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.status_label.config(text="监控已停止", foreground="orange")
    
    def monitor_process(self):
        try:
            cmd = f'resmon.exe /pid {self.yuanshen_pid}'
            
            self.log_message(f"开始监控原神进程 (PID: {self.yuanshen_pid})")
            self.log_message(f"启动资源监视器命令: {cmd}")
            
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, text=True)
            
            while self.monitoring:
                if not self.is_process_running(self.yuanshen_pid):
                    self.log_message("原神进程已结束")
                    break
                
                self.monitor_file_access()
                time.sleep(1)
                
        except Exception as e:
            self.log_message(f"监控过程中发生错误: {str(e)}")
    
    def is_process_running(self, pid):
        try:
            return psutil.pid_exists(pid)
        except:
            return False
    
    def monitor_file_access(self):
        try:
            proc = psutil.Process(self.yuanshen_pid)
            
            try:
                open_files = proc.open_files()
                for file_info in open_files:
                    file_path = file_info.path
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    log_entry = f"[{timestamp}] 文件访问: {file_path}"
                    
                    if log_entry not in [entry['message'] for entry in self.log_data[-10:]]:
                        self.log_message(log_entry)
                        
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                pass
                
        except Exception as e:
            if self.monitoring:
                self.log_message(f"获取文件信息时出错: {str(e)}")
    
    def log_message(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = {
            'timestamp': timestamp,
            'message': message
        }
        self.log_data.append(log_entry)
        
        self.root.after(0, lambda: self.update_log_display(f"[{timestamp}] {message}"))
    
    def update_log_display(self, message):
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
    
    def update_stats(self):
        if hasattr(self, 'start_time'):
            elapsed_time = int(time.time() - self.start_time)
            file_count = len([entry for entry in self.log_data if "文件访问:" in entry['message']])
            self.stats_label.config(text=f"文件访问次数: {file_count} | 监控时间: {elapsed_time}秒")
        
        if self.monitoring:
            self.root.after(1000, self.update_stats)
    
    def clear_log(self):
        self.log_text.delete(1.0, tk.END)
        self.log_data.clear()
    
    def save_log(self):
        if not self.log_data:
            messagebox.showinfo("提示", "没有日志数据可保存")
            return
        
        filename = f"yuanshen_monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("原神进程文件访问监控日志\n")
                f.write("=" * 50 + "\n\n")
                for entry in self.log_data:
                    f.write(f"[{entry['timestamp']}] {entry['message']}\n")
            
            messagebox.showinfo("成功", f"日志已保存到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {str(e)}")

def main():
    root = tk.Tk()
    app = YuanshenMonitor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
