import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import subprocess
import threading
import time
import os
import sys
import ctypes
from datetime import datetime
import psutil
import re

class YuanshenMonitor:
    def __init__(self, root):
        self.root = root
        self.root.title("原神进程文件监控器")
        self.root.geometry("1200x800")

        self.monitoring = False
        self.yuanshen_pid = None
        self.log_data = []
        self.file_access_count = {}
        self.prefix_filter = ""
        self.suffix_filter = ""

        self.setup_ui()
        self.check_admin_privileges()
        
    def check_admin_privileges(self):
        try:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if not is_admin:
                messagebox.showwarning("权限警告", "建议以管理员身份运行此程序以获得完整的监控功能")
        except:
            pass
    
    def setup_ui(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

        ttk.Label(main_frame, text="原神进程监控", font=("Arial", 16, "bold")).grid(row=0, column=0, columnspan=3, pady=(0, 20))

        filter_frame = ttk.LabelFrame(main_frame, text="文件过滤设置", padding="5")
        filter_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(filter_frame, text="前缀过滤:").grid(row=0, column=0, padx=(0, 5), sticky=tk.W)
        self.prefix_entry = ttk.Entry(filter_frame, width=20)
        self.prefix_entry.grid(row=0, column=1, padx=(0, 20), sticky=tk.W)
        self.prefix_entry.insert(0, "*")

        ttk.Label(filter_frame, text="后缀过滤:").grid(row=0, column=2, padx=(0, 5), sticky=tk.W)
        self.suffix_entry = ttk.Entry(filter_frame, width=20)
        self.suffix_entry.grid(row=0, column=3, padx=(0, 20), sticky=tk.W)
        self.suffix_entry.insert(0, "*")

        ttk.Button(filter_frame, text="应用过滤", command=self.apply_filter).grid(row=0, column=4, padx=(10, 0))

        ttk.Label(filter_frame, text="提示: 使用*或留空表示任意，例如: 前缀'C:\\Game' 后缀'.dll'",
                 font=("Arial", 8)).grid(row=1, column=0, columnspan=5, pady=(5, 0), sticky=tk.W)

        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 10))

        self.start_btn = ttk.Button(control_frame, text="开始监控", command=self.start_monitoring)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_btn = ttk.Button(control_frame, text="清空日志", command=self.clear_log)
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.save_btn = ttk.Button(control_frame, text="保存日志", command=self.save_log)
        self.save_btn.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(control_frame, text="进程状态:").pack(side=tk.LEFT, padx=(20, 5))
        self.status_label = ttk.Label(control_frame, text="未检测到原神进程", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        log_frame = ttk.LabelFrame(main_frame, text="文件访问日志 (显示调用次数)", padding="5")
        log_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=25, font=("Consolas", 9))
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        stats_frame = ttk.LabelFrame(main_frame, text="统计信息", padding="5")
        stats_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        self.stats_label = ttk.Label(stats_frame, text="文件访问次数: 0 | 监控时间: 0秒 | 当前过滤: 前缀(*) 后缀(*)")
        self.stats_label.pack()
        
    def apply_filter(self):
        prefix = self.prefix_entry.get().strip()
        suffix = self.suffix_entry.get().strip()

        self.prefix_filter = "" if prefix == "*" or prefix == "" else prefix
        self.suffix_filter = "" if suffix == "*" or suffix == "" else suffix

        prefix_display = self.prefix_filter if self.prefix_filter else "*"
        suffix_display = self.suffix_filter if self.suffix_filter else "*"

        self.update_stats_display()
        messagebox.showinfo("过滤设置", f"过滤器已应用:\n前缀: {prefix_display}\n后缀: {suffix_display}")

    def matches_filter(self, file_path):
        if self.prefix_filter and not file_path.startswith(self.prefix_filter):
            return False
        if self.suffix_filter and not file_path.endswith(self.suffix_filter):
            return False
        return True

    def find_yuanshen_process(self):
        yuanshen_names = ["YuanShen.exe", "GenshinImpact.exe", "原神.exe"]
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'] in yuanshen_names:
                    return proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return None
    
    def start_monitoring(self):
        self.yuanshen_pid = self.find_yuanshen_process()
        if not self.yuanshen_pid:
            messagebox.showerror("错误", "未找到原神进程，请先启动原神游戏")
            return

        self.apply_filter()

        self.monitoring = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.status_label.config(text=f"正在监控原神进程 (PID: {self.yuanshen_pid})", foreground="green")

        self.file_access_count.clear()

        self.monitor_thread = threading.Thread(target=self.monitor_process, daemon=True)
        self.monitor_thread.start()

        self.start_time = time.time()
        self.update_stats()
    
    def stop_monitoring(self):
        self.monitoring = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.status_label.config(text="监控已停止", foreground="orange")
    
    def monitor_process(self):
        try:
            cmd = f'resmon.exe /pid {self.yuanshen_pid}'
            
            self.log_message(f"开始监控原神进程 (PID: {self.yuanshen_pid})")
            self.log_message(f"启动资源监视器命令: {cmd}")
            
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, text=True)
            
            while self.monitoring:
                if not self.is_process_running(self.yuanshen_pid):
                    self.log_message("原神进程已结束")
                    break
                
                self.monitor_file_access()
                time.sleep(1)
                
        except Exception as e:
            self.log_message(f"监控过程中发生错误: {str(e)}")
    
    def is_process_running(self, pid):
        try:
            return psutil.pid_exists(pid)
        except:
            return False
    
    def monitor_file_access(self):
        try:
            proc = psutil.Process(self.yuanshen_pid)

            try:
                open_files = proc.open_files()
                for file_info in open_files:
                    file_path = file_info.path

                    if self.matches_filter(file_path):
                        if file_path in self.file_access_count:
                            self.file_access_count[file_path] += 1
                            self.update_file_count_display(file_path)
                        else:
                            self.file_access_count[file_path] = 1
                            timestamp = datetime.now().strftime("%H:%M:%S")
                            log_entry = f"[{timestamp}] 文件访问: {file_path} (调用次数: 1)"
                            self.log_message(log_entry)

            except (psutil.AccessDenied, psutil.NoSuchProcess):
                pass

        except Exception as e:
            if self.monitoring:
                self.log_message(f"获取文件信息时出错: {str(e)}")
    
    def update_file_count_display(self, file_path):
        count = self.file_access_count[file_path]

        content = self.log_text.get(1.0, tk.END)
        lines = content.split('\n')

        for i, line in enumerate(lines):
            if file_path in line and "调用次数:" in line:
                old_count_match = re.search(r'调用次数: (\d+)', line)
                if old_count_match:
                    new_line = re.sub(r'调用次数: \d+', f'调用次数: {count}', line)

                    line_start = f"{i+1}.0"
                    line_end = f"{i+1}.end"
                    self.log_text.delete(line_start, line_end)
                    self.log_text.insert(line_start, new_line)
                    break

    def log_message(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = {
            'timestamp': timestamp,
            'message': message
        }
        self.log_data.append(log_entry)

        self.root.after(0, lambda: self.update_log_display(message))

    def update_log_display(self, message):
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
    
    def update_stats(self):
        if hasattr(self, 'start_time'):
            elapsed_time = int(time.time() - self.start_time)
            unique_files = len(self.file_access_count)
            total_accesses = sum(self.file_access_count.values())

            prefix_display = self.prefix_filter if self.prefix_filter else "*"
            suffix_display = self.suffix_filter if self.suffix_filter else "*"

            self.stats_label.config(text=f"独立文件: {unique_files} | 总访问次数: {total_accesses} | 监控时间: {elapsed_time}秒 | 过滤: 前缀({prefix_display}) 后缀({suffix_display})")

        if self.monitoring:
            self.root.after(1000, self.update_stats)

    def update_stats_display(self):
        if hasattr(self, 'start_time'):
            elapsed_time = int(time.time() - self.start_time)
            unique_files = len(self.file_access_count)
            total_accesses = sum(self.file_access_count.values())
        else:
            elapsed_time = 0
            unique_files = 0
            total_accesses = 0

        prefix_display = self.prefix_filter if self.prefix_filter else "*"
        suffix_display = self.suffix_filter if self.suffix_filter else "*"

        self.stats_label.config(text=f"独立文件: {unique_files} | 总访问次数: {total_accesses} | 监控时间: {elapsed_time}秒 | 过滤: 前缀({prefix_display}) 后缀({suffix_display})")
    
    def clear_log(self):
        self.log_text.delete(1.0, tk.END)
        self.log_data.clear()
        self.file_access_count.clear()
        self.update_stats_display()

    def save_log(self):
        if not self.log_data and not self.file_access_count:
            messagebox.showinfo("提示", "没有日志数据可保存")
            return

        filename = f"yuanshen_monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("原神进程文件访问监控日志\n")
                f.write("=" * 50 + "\n")
                f.write(f"过滤设置: 前缀({self.prefix_filter if self.prefix_filter else '*'}) 后缀({self.suffix_filter if self.suffix_filter else '*'})\n")
                f.write(f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 50 + "\n\n")

                f.write("文件访问统计:\n")
                f.write("-" * 30 + "\n")
                for file_path, count in sorted(self.file_access_count.items(), key=lambda x: x[1], reverse=True):
                    f.write(f"调用次数: {count:4d} | {file_path}\n")

                f.write("\n" + "=" * 50 + "\n")
                f.write("详细日志:\n")
                f.write("-" * 30 + "\n")
                for entry in self.log_data:
                    f.write(f"[{entry['timestamp']}] {entry['message']}\n")

            messagebox.showinfo("成功", f"日志已保存到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {str(e)}")

def main():
    root = tk.Tk()
    app = YuanshenMonitor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
